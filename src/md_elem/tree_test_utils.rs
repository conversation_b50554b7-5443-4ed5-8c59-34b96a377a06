#[cfg(test)]
pub(crate) use test_utils::*;

#[cfg(test)]
mod test_utils {
    macro_rules! md_elem {
        ( $($node_names:ident)::* {$($attr:ident: $val:expr),* $(,)?}) => {
            crate::md_elem::m_node!(MdElem::$($node_names)::* {$($attr: $val),*})
        };
        ($paragraph_text:literal) => {
            crate::md_elem::m_node!(MdElem::Paragraph{body: vec![mdq_inline!($paragraph_text)]})
        };
    }
    pub(crate) use md_elem;

    macro_rules! md_elems {
        [$($first:tt $( $(:: $($rest:ident)::* )? {$($attr:ident: $val:expr),*$(,)?})? ),*$(,)?] => {
            vec![$(
                md_elem!($first$( $(:: $($rest)::*)? { $($attr: $val),* })?)
                ),*
            ]
        };
    }
    pub(crate) use md_elems;

    macro_rules! mdq_inline {
        (span $which:ident [$($contents:expr),*$(,)?]) => {
            crate::md_elem::elem::Inline::Span(Span {
                variant: crate::md_elem::elem::SpanVariant::$which,
                children: vec![$($contents),*],
            })
        };
        ($text:literal) => {
            crate::md_elem::elem::Inline::Text(Text {
                variant: crate::md_elem::elem::TextVariant::Plain,
                value: $text.to_string(),
            })
        };
    }
    use crate::md_elem::elem::BlockHtml;
    pub(crate) use mdq_inline;

    macro_rules! inlines {
        // Empty case
        [] => {
            Vec::<crate::md_elem::tree::elem::Inline>::new()
        };

        // Single string literal
        [$text:literal] => {
            vec![
                crate::md_elem::tree::elem::Inline::Text(crate::md_elem::tree::elem::Text {
                    variant: crate::md_elem::tree::elem::TextVariant::Plain,
                    value: $text.to_string(),
                })
            ]
        };

        // String literal followed by more content
        [$first:literal, $($rest:tt)*] => {
            {
                let mut result = vec![
                    crate::md_elem::tree::elem::Inline::Text(crate::md_elem::tree::elem::Text {
                        variant: crate::md_elem::tree::elem::TextVariant::Plain,
                        value: $first.to_string(),
                    })
                ];
                result.extend(inlines![$($rest)*]);
                result
            }
        };

        // Emphasis with content
        [em([$($content:tt)*])] => {
            vec![
                crate::md_elem::tree::elem::Inline::Span(crate::md_elem::tree::elem::Span {
                    variant: crate::md_elem::tree::elem::SpanVariant::Emphasis,
                    children: inlines![$($content)*],
                })
            ]
        };

        // Strong with content
        [strong([$($content:tt)*])] => {
            vec![
                crate::md_elem::tree::elem::Inline::Span(crate::md_elem::tree::elem::Span {
                    variant: crate::md_elem::tree::elem::SpanVariant::Strong,
                    children: inlines![$($content)*],
                })
            ]
        };

        // Link with display text and URL
        [link([$($display:tt)*] $url:literal)] => {
            vec![
                crate::md_elem::tree::elem::Inline::Link(crate::md_elem::tree::elem::Link::Standard(
                    crate::md_elem::tree::elem::StandardLink {
                        display: inlines![$($display)*],
                        link: crate::md_elem::tree::elem::LinkDefinition {
                            url: $url.to_string(),
                            title: None,
                            reference: crate::md_elem::tree::elem::LinkReference::Inline,
                        },
                    }
                ))
            ]
        };

        // Emphasis followed by more content
        [em([$($em_content:tt)*]), $($rest:tt)*] => {
            {
                let mut result = vec![
                    crate::md_elem::tree::elem::Inline::Span(crate::md_elem::tree::elem::Span {
                        variant: crate::md_elem::tree::elem::SpanVariant::Emphasis,
                        children: inlines![$($em_content)*],
                    })
                ];
                result.extend(inlines![$($rest)*]);
                result
            }
        };

        // Strong followed by more content
        [strong([$($strong_content:tt)*]), $($rest:tt)*] => {
            {
                let mut result = vec![
                    crate::md_elem::tree::elem::Inline::Span(crate::md_elem::tree::elem::Span {
                        variant: crate::md_elem::tree::elem::SpanVariant::Strong,
                        children: inlines![$($strong_content)*],
                    })
                ];
                result.extend(inlines![$($rest)*]);
                result
            }
        };

        // Link followed by more content
        [link([$($display:tt)*] $url:literal), $($rest:tt)*] => {
            {
                let mut result = vec![
                    crate::md_elem::tree::elem::Inline::Link(crate::md_elem::tree::elem::Link::Standard(
                        crate::md_elem::tree::elem::StandardLink {
                            display: inlines![$($display)*],
                            link: crate::md_elem::tree::elem::LinkDefinition {
                                url: $url.to_string(),
                                title: None,
                                reference: crate::md_elem::tree::elem::LinkReference::Inline,
                            },
                        }
                    ))
                ];
                result.extend(inlines![$($rest)*]);
                result
            }
        };
    }
    pub(crate) use inlines;

    impl From<&str> for BlockHtml {
        fn from(value: &str) -> Self {
            Self {
                value: value.to_string(),
            }
        }
    }
}

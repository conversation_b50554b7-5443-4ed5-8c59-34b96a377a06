use crate::md_elem::flat_inlines::{FlattenedText, FormattingType, RegexReplaceError, RangeReplacementError};
use crate::md_elem::tree::elem::Inline;

/// Applies regex search and replace to a vector of inline elements.
///
/// This flattens the inlines, applies the regex replacement, and reconstructs
/// the tree structure. Returns an error if the regex would cross formatting
/// boundaries that cannot be represented (like links or unsupported content).
pub(crate) fn regex_replace_inlines(
    inlines: &[Inline],
    pattern: &fancy_regex::Regex,
    replacement: &str
) -> Result<Vec<Inline>, RegexReplaceError> {
    // 1. Flatten the inlines
    let mut flattened = FlattenedText::from_inlines(inlines)?;

    // 2. Find all regex matches to check for unsupported content overlaps
    let matches: Vec<_> = pattern.find_iter(&flattened.text)
        .collect::<Result<Vec<_>, _>>()
        .map_err(|_| RegexReplaceError {})?;

    // 3. Check if any matches cross unsupported formatting boundaries
    for mat in &matches {
        let match_start = mat.start();
        let match_end = mat.end();

        for event in &flattened.formatting_events {
            if matches!(event.formatting, FormattingType::Unsupported) {
                let event_start = event.start_pos;
                let event_end = event.start_pos + event.length;

                // Check if the match overlaps with this unsupported event
                if match_start < event_end && match_end > event_start {
                    return Err(RegexReplaceError {});
                }
            }
        }
    }

    // 4. If no matches found, return the original inlines unchanged
    if matches.is_empty() {
        return Ok(inlines.to_vec());
    }

    // 5. Apply range replacements to update formatting events
    // We need to apply them in reverse order to maintain correct positions
    for mat in matches.iter().rev() {
        // Get the replacement text for this specific match using captures
        let captures = pattern.captures(&flattened.text[mat.start()..mat.end()])
            .map_err(|_| RegexReplaceError {})?
            .ok_or(RegexReplaceError {})?;

        let replacement_text = pattern.replace(&flattened.text[mat.start()..mat.end()], replacement)
            .map_err(|_| RegexReplaceError {})?;

        flattened.replace_range(mat.start()..mat.end(), &replacement_text)
            .map_err(|_| RegexReplaceError {})?;
    }

    // 6. Reconstruct the inlines
    flattened.unflatten()
}
